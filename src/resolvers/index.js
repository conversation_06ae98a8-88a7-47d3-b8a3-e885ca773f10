import { markdownToAdf } from 'marklassian';
import { getCustomFieldId, updateIssueFields, isFieldUpdated, getFieldChangeValue, validateEnvironment } from '../utils/jira-helpers.js';
import { expandRequirements } from '../utils/openrouter-client.js';

// --- Handler for Issue Update Trigger (Optimized for Simple Requirements Field) ---

export const simpleRequirementsUpdatedHandler = async (event, context) => {
  console.log('Issue Updated Event Received:', JSON.stringify(event));
  console.log('Context:', JSON.stringify(context));

  const issueId = event.issue.id;
  const issueKey = event.issue.key;

  console.log(`Processing issue ${issueKey} (ID: ${issueId})`);

  // 1. Validate environment
  const envValidation = validateEnvironment();
  if (!envValidation.isValid) {
    console.error(`Missing required environment variables: ${envValidation.missing.join(', ')}`);
    console.error('Ensure OpenRouter API key was set using `forge variables set --encrypt SECRET_OPENROUTER_API_KEY`.');
    return;
  }

  // 2. Get Custom Field information using API
  const simpleReqField = await getCustomFieldId('simple-requirements-field');
  if (!simpleReqField) {
    console.error('Could not resolve Simple Requirements field information');
    return;
  }

  const fullReqField = await getCustomFieldId('full-requirements-field');
  if (!fullReqField) {
    console.error('Could not resolve Full Requirements field information');
    return;
  }

  console.log(`Field IDs - Simple: ${simpleReqField.id}, Full: ${fullReqField.id}`);
  console.log(`Field Types - Simple: ${simpleReqField.schema?.type}, Full: ${fullReqField.schema?.type}`);

  // 3. Check if the Simple Requirements field was actually updated in this event
  const changelog = event.changelog;
  if (!changelog || !changelog.items) {
    console.log(`No changelog found in event. Skipping.`);
    return;
  }

  if (!isFieldUpdated(changelog, simpleReqField)) {
    console.log(`Simple Requirements field was not updated in this event. Skipping.`);
    return;
  }

  console.log(`Simple Requirements field was updated. Processing...`);

  // 4. Get the new value of the Simple Requirements field from the changelog
  const simpleRequirements = getFieldChangeValue(changelog, simpleReqField) || '';
  console.log(`Simple Requirements Input: ${simpleRequirements}`);

  if (!simpleRequirements || simpleRequirements.trim() === '') {
    console.log(`Simple Requirements field is empty for issue ${issueKey}. Skipping.`);
    return;
  }

  // 5. Call OpenRouter API to expand requirements
  const fullRequirements = await expandRequirements(simpleRequirements);
  if (!fullRequirements) {
    console.error('Failed to expand requirements using OpenRouter API');
    return;
  }

  // 6. Convert Markdown to ADF format and update the issue
  console.log(`Converting Markdown to ADF format...`);
  const adfContent = markdownToAdf(fullRequirements);

  console.log(`Updating issue ${issueKey} field ${fullReqField.id} with ADF content...`);

  const updateSuccess = await updateIssueFields(issueId, {
    [fullReqField.id]: adfContent
  });

  if (updateSuccess) {
    console.log(`Successfully updated 'Full Requirements' field (${fullReqField.id}) for issue ${issueKey} with rich text formatted content.`);
  } else {
    console.error(`Failed to update issue ${issueKey} with expanded requirements.`);
  }
  };

