/**
 * OpenRouter API Client
 * 
 * This module provides functions for interacting with the OpenRouter API
 * to generate expanded requirements using AI models.
 */

import api from '@forge/api';

/**
 * Default configuration for OpenRouter API calls
 */
const DEFAULT_CONFIG = {
  model: 'deepseek/deepseek-chat-v3-0324:free',
  temperature: 0.7,
  maxTokens: 500,
  timeoutMs: 20000
};

/**
 * Generate expanded requirements using OpenRouter API
 * @param {string} simpleRequirements - The simple requirements input
 * @param {Object} options - Optional configuration overrides
 * @returns {Promise<string|null>} The expanded requirements or null if failed
 */
export async function expandRequirements(simpleRequirements, options = {}) {
  const config = { ...DEFAULT_CONFIG, ...options };
  const apiKey = process.env.SECRET_OPENROUTER_API_KEY;

  if (!apiKey) {
    console.error('OpenRouter API Key (SECRET_OPENROUTER_API_KEY) is not set in Forge environment variables.');
    return null;
  }

  if (!simpleRequirements || simpleRequirements.trim() === '') {
    console.error('Simple requirements input is empty');
    return null;
  }

  const prompt = createPrompt(simpleRequirements);

  try {
    console.log(`Calling OpenRouter API with ${config.model}`);

    // Create a timeout promise to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`OpenRouter API request timed out after ${config.timeoutMs/1000} seconds`)), config.timeoutMs);
    });

    // Create the actual API request promise
    const apiRequestPromise = api.fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://project0jiraplugin.atlassian.net',
        'X-Title': 'Jira Requirements Expander',
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          { role: 'system', content: 'You are a helpful assistant that expands requirements for Jira user stories.' },
          { role: 'user', content: prompt },
        ],
        temperature: config.temperature,
        max_tokens: config.maxTokens,
      }),
    });

    // Race between the API request and timeout
    const response = await Promise.race([apiRequestPromise, timeoutPromise]);

    if (!response.ok) {
      const errorBody = await response.text();
      console.error(`OpenRouter API request failed with ${response.status} ${response.statusText}`);
      console.error('OpenRouter Error Body:', errorBody);
      return null;
    }

    const data = await response.json();

    if (data.choices && data.choices.length > 0 && data.choices[0].message) {
      const expandedRequirements = data.choices[0].message.content.trim();
      console.log('OpenRouter Response Received (Markdown Format):', expandedRequirements);
      return expandedRequirements;
    } else {
      console.error('OpenRouter response format unexpected or empty.', JSON.stringify(data));
      return null;
    }
  } catch (error) {
    console.error(`Error calling OpenRouter API:`, error);
    return null;
  }
}

/**
 * Create a prompt for the AI model
 * @param {string} simpleRequirements - The simple requirements input
 * @returns {string} The formatted prompt
 */
function createPrompt(simpleRequirements) {
  return `Expand the following simple requirements into detailed, structured, actionable user story requirements:

"${simpleRequirements}"

Format the output using Markdown. Reply only with Markdown Requirements Output not wrapped in a code block with no extra response components.`;
}

/**
 * Validate OpenRouter API configuration
 * @returns {Object} Object with isValid boolean and errors array
 */
export function validateOpenRouterConfig() {
  const errors = [];
  
  if (!process.env.SECRET_OPENROUTER_API_KEY) {
    errors.push('SECRET_OPENROUTER_API_KEY environment variable is not set');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
