/**
 * <PERSON>ra Helper Functions for Forge App
 *
 * This module contains helper functions for interacting with <PERSON><PERSON> using the Forge API.
 * These functions are designed to work within the Forge runtime environment.
 */

import api, { route } from '@forge/api';

export const FIELD_NAME_MAP = {
  'simple-requirements-field': 'Simple Requirements',
  'full-requirements-field': 'Full Requirements'
};

/**
 * Get custom field information by field key using Forge API
 * @param {string} fieldKey - The field key (e.g., 'simple-requirements-field')
 * @returns {Promise<Object|null>} Field object with id, name, schema, etc. or null if not found
 */
export async function getCustomFieldId(fieldKey) {
  try {
    // Query the API to get all fields and find our custom fields
    const fieldsResponse = await api.asApp().requestJira(route`/rest/api/3/field`);
    if (!fieldsResponse.ok) {
      console.error(`Failed to fetch fields: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
      return null;
    }

    const fieldsData = await fieldsResponse.json();
    const targetFieldName = FIELD_NAME_MAP[fieldKey];
    
    if (!targetFieldName) {
      console.error(`Unknown field key: ${fieldKey}`);
      return null;
    }

    // Find the field by name
    const foundField = fieldsData.find(f => f.name === targetFieldName);
    if (!foundField) {
      console.error(`Could not find custom field with name: ${targetFieldName}`);
      return null;
    }

    console.log(`Found field ${targetFieldName} with ID: ${foundField.id}, Type: ${foundField.schema?.type}, Custom: ${foundField.schema?.custom}`);
    return foundField;
  } catch (error) {
    console.error(`Error fetching custom field ID for ${fieldKey}:`, error);
    return null;
  }
}

/**
 * Update a Jira issue with the given fields using Forge API
 * @param {string} issueId - The issue ID
 * @param {Object} fields - Object containing field IDs as keys and values to update
 * @returns {Promise<boolean>} True if successful, false otherwise
 */
export async function updateIssueFields(issueId, fields) {
  try {
    const updateResponse = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}`, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ fields })
    });

    if (!updateResponse.ok) {
      const errorBody = await updateResponse.text();
      console.error(`Failed to update issue ${issueId}: ${updateResponse.status} ${updateResponse.statusText}`);
      console.error('Update Error Body:', errorBody);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error updating issue ${issueId}:`, error);
    return false;
  }
}

/**
 * Check if a specific field was updated in the changelog
 * @param {Object} changelog - The changelog object from the event
 * @param {Object} field - The field object with id and name
 * @returns {boolean} True if the field was updated
 */
export function isFieldUpdated(changelog, field) {
  if (!changelog || !changelog.items || !field) {
    return false;
  }

  return changelog.items.some(item =>
    item.fieldId === field.id || item.field === field.name
  );
}

/**
 * Get the new value of a field from the changelog
 * @param {Object} changelog - The changelog object from the event
 * @param {Object} field - The field object with id and name
 * @returns {string|null} The new value or null if not found
 */
export function getFieldChangeValue(changelog, field) {
  if (!changelog || !changelog.items || !field) {
    return null;
  }

  const fieldChange = changelog.items.find(item =>
    item.fieldId === field.id || item.field === field.name
  );

  return fieldChange?.to || null;
}

/**
 * Validate that required environment variables are set
 * @returns {Object} Object with isValid boolean and missing array
 */
export function validateEnvironment() {
  const required = ['SECRET_OPENROUTER_API_KEY'];
  const missing = required.filter(key => !process.env[key]);
  
  return {
    isValid: missing.length === 0,
    missing
  };
}
