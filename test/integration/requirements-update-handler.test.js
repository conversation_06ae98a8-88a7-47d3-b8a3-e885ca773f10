import { test, describe, before, after } from 'node:test';
import assert from 'node:assert';
import { config, validateConfig } from '../config.js';
import { getCustomFieldId, getIssue, updateIssue } from '../utils/jira-api-client.js';
import { wait, logTestStep, logTestResult } from '../utils/test-helpers.js';

describe('RequirementsUpdateHandler Integration Test', () => {
  let simpleReqFieldId = null;
  let fullReqFieldId = null;
  let originalSimpleValue = null;
  let originalFullValue = null;



  before(async () => {
    console.log('Setting up integration test...');

    // Validate configuration
    validateConfig();

    // Get field IDs
    simpleReqFieldId = await getCustomFieldId('Simple Requirements');
    fullReqFieldId = await getCustomFieldId('Full Requirements');
    
    console.log(`Simple Requirements Field ID: ${simpleReqFieldId}`);
    console.log(`Full Requirements Field ID: ${fullReqFieldId}`);

    // Get current issue state to restore later
    const issue = await getIssue(config.TEST_ISSUE_ID);
    originalSimpleValue = issue.fields[simpleReqFieldId];
    originalFullValue = issue.fields[fullReqFieldId];
    
    console.log(`Original Simple Requirements: ${originalSimpleValue}`);
    console.log(`Original Full Requirements: ${JSON.stringify(originalFullValue)}`);
  });

  after(async () => {
    console.log('Cleaning up integration test...');
    
    // Restore original values
    const restoreFields = {};
    if (originalSimpleValue !== null) {
      restoreFields[simpleReqFieldId] = originalSimpleValue;
    }
    if (originalFullValue !== null) {
      restoreFields[fullReqFieldId] = originalFullValue;
    }
    
    if (Object.keys(restoreFields).length > 0) {
      await updateIssue(config.TEST_ISSUE_ID, restoreFields);
      console.log('Original field values restored');
    }
  });

  test('should update FullRequirements field when SimpleRequirements field is updated', async () => {
    logTestStep(0, 'Starting integration test');

    // Step 1: Clear the FullRequirements field and set SimpleRequirements field
    const testSimpleRequirements = 'Test requirement: Create a user login system with authentication. \n\n Keep the Requirements very short!';
    const updateFields = {
      [simpleReqFieldId]: testSimpleRequirements,
      [fullReqFieldId]: null // Clear the full requirements field
    };

    logTestStep(1, 'Updating SimpleRequirements field and clearing FullRequirements field', {
      input: testSimpleRequirements,
      expected: 'FullRequirements field should be populated by the handler'
    });

    await updateIssue(config.TEST_ISSUE_ID, updateFields);

    // Verify the update was successful
    let issue = await getIssue(config.TEST_ISSUE_ID);
    assert.strictEqual(
      issue.fields[simpleReqFieldId], 
      testSimpleRequirements, 
      'SimpleRequirements field should be updated'
    );
    assert.strictEqual(
      issue.fields[fullReqFieldId], 
      null, 
      'FullRequirements field should be cleared'
    );
    
    logTestStep(2, 'Waiting for the handler to process the update', 'This may take up to 60 seconds...');

    // Poll for changes every 5 seconds for up to 60 seconds
    let attempts = 0;
    const maxAttempts = 12; // 12 * 5 = 60 seconds
    let updatedFullRequirements = null;

    while (attempts < maxAttempts) {
      await wait(5);
      attempts++;

      console.log(`   Checking attempt ${attempts}/${maxAttempts}...`);
      issue = await getIssue(config.TEST_ISSUE_ID);
      updatedFullRequirements = issue.fields[fullReqFieldId];

      if (updatedFullRequirements !== null) {
        logTestResult(true, `FullRequirements field updated after ${attempts * 5} seconds!`);
        break;
      }
    }

    if (attempts >= maxAttempts) {
      logTestResult(false, 'FullRequirements field still null after 60 seconds', {
        troubleshooting: [
          'The Forge app trigger is not activated by API updates',
          'The RequirementsUpdateHandler is not processing the event',
          'The OpenRouter API key is not configured correctly',
          'The handler is encountering an error',
          'The handler takes longer than 60 seconds to process'
        ],
        nextSteps: [
          'Check Forge app logs: `forge logs`',
          'Verify OpenRouter API key is set: `forge variables list`',
          'Try updating the field manually in Jira UI',
          'Check if the trigger works with UI updates vs API updates'
        ]
      });
    }
    
    // Step 3: Final verification
    logTestStep(3, 'Final verification of FullRequirements field', JSON.stringify(updatedFullRequirements));

    // Verify that the FullRequirements field was updated with content
    assert.notStrictEqual(
      updatedFullRequirements,
      null,
      'FullRequirements field should not be null after processing'
    );

    // For ADF content, check if it has the expected structure
    if (updatedFullRequirements && typeof updatedFullRequirements === 'object') {

      // Check if there's actual content (not just empty structure)
      if (updatedFullRequirements.content && Array.isArray(updatedFullRequirements.content)) {
        assert.ok(
          updatedFullRequirements.content.length > 0,
          'FullRequirements field should contain non-empty content'
        );
      }
    } else if (typeof updatedFullRequirements === 'string') {
      assert.ok(
        updatedFullRequirements.trim().length > 0,
        'FullRequirements field should contain non-empty string content'
      );
    }

    logTestResult(true, 'Integration test completed successfully!');
  });
});
