import { readFileSync } from 'node:fs';
import { join, dirname } from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Simple .env file loader with quote handling
function loadEnv() {
  try {
    const envPath = join(__dirname, '.env');
    const envContent = readFileSync(envPath, 'utf8');

    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const equalIndex = trimmedLine.indexOf('=');
        if (equalIndex > 0) {
          const key = trimmedLine.substring(0, equalIndex).trim();
          let value = trimmedLine.substring(equalIndex + 1).trim();

          // Remove surrounding quotes if present
          if ((value.startsWith('"') && value.endsWith('"')) ||
              (value.startsWith("'") && value.endsWith("'"))) {
            value = value.slice(1, -1);
          }

          process.env[key] = value;
        }
      }
    });
  } catch (error) {
    // .env file doesn't exist or can't be read, use environment variables or defaults
    console.log('No .env file found, using environment variables or defaults');
  }
}

// Load environment variables
loadEnv();

// Export configuration
export const config = {
  JIRA_BASE_URL: process.env.JIRA_BASE_URL || 'https://your-domain.atlassian.net',
  JIRA_ACCESS_TOKEN: process.env.JIRA_ACCESS_TOKEN || 'YOUR_ACCESS_TOKEN_HERE',
  JIRA_EMAIL: process.env.JIRA_EMAIL || '<EMAIL>',
  TEST_ISSUE_ID: process.env.TEST_ISSUE_ID || '10139'
};

// Validate configuration
export function validateConfig() {
  const errors = [];
  
  if (config.JIRA_BASE_URL === 'https://your-domain.atlassian.net') {
    errors.push('JIRA_BASE_URL is not configured');
  }
  
  if (config.JIRA_ACCESS_TOKEN === 'YOUR_ACCESS_TOKEN_HERE') {
    errors.push('JIRA_ACCESS_TOKEN is not configured');
  }
  
  if (config.JIRA_EMAIL === '<EMAIL>') {
    errors.push('JIRA_EMAIL is not configured');
  }
  
  if (errors.length > 0) {
    console.error('❌ Configuration errors:');
    errors.forEach(error => console.error(`  - ${error}`));
    console.error('\n📝 Please create a test/.env file with your Jira credentials.');
    console.error('   See test/.env.example for the required format.');
    console.error('\n🔗 Create an API token at: https://id.atlassian.com/manage-profile/security/api-tokens');
    throw new Error('Configuration validation failed');
  }
  
  console.log('✅ Configuration validated successfully');
  console.log(`   Jira URL: ${config.JIRA_BASE_URL}`);
  console.log(`   Email: ${config.JIRA_EMAIL}`);
  console.log(`   Test Issue: ${config.TEST_ISSUE_ID}`);
}
