import { jest } from '@jest/globals';

// Create mock for @forge/api
const mockRequestJira = jest.fn();
const mockRoute = jest.fn((template, ...args) => {
  if (template.raw) {
    return template.raw.join('');
  }
  return template;
});

const mockApi = {
  asApp: jest.fn(() => ({
    requestJira: mockRequestJira
  }))
};

jest.unstable_mockModule('@forge/api', () => ({
  default: mockApi,
  route: mockRoute
}));

// Import the module under test after mocking
const {
  getCustomFieldId,
  updateIssueFields,
  isFieldUpdated,
  getFieldChangeValue,
  validateEnvironment,
  FIELD_NAME_MAP
} = await import('../../../src/utils/jira-helpers.js');

// Test data
const mockJiraFields = [
  { id: 'customfield_10001', name: 'Simple Requirements', schema: { type: 'string', custom: 'textarea' } },
  { id: 'customfield_10002', name: 'Full Requirements', schema: { type: 'any', custom: 'textarea' } }
];

describe('jira-helpers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('FIELD_NAME_MAP', () => {
    it('should have correct field name mappings', () => {
      expect(FIELD_NAME_MAP['simple-requirements-field']).toBe('Simple Requirements');
      expect(FIELD_NAME_MAP['full-requirements-field']).toBe('Full Requirements');
    });
  });

  describe('getCustomFieldId', () => {
    it('should return field information for valid field key', async () => {
      mockRequestJira.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue(mockJiraFields)
      });

      const result = await getCustomFieldId('simple-requirements-field');

      expect(result).toEqual(mockJiraFields[0]);
      expect(mockRequestJira).toHaveBeenCalledWith(expect.any(String));
    });

    it('should return null for unknown field key', async () => {
      const result = await getCustomFieldId('unknown-field');

      expect(result).toBeNull();
      expect(mockRequestJira).not.toHaveBeenCalled();
    });

    it('should return null if field is not found in Jira', async () => {
      mockRequestJira.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue([])
      });

      const result = await getCustomFieldId('simple-requirements-field');

      expect(result).toBeNull();
    });

    it('should return null if API request fails', async () => {
      mockRequestJira.mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      const result = await getCustomFieldId('simple-requirements-field');

      expect(result).toBeNull();
    });

    it('should handle API request exceptions', async () => {
      mockRequestJira.mockRejectedValue(new Error('Network error'));

      const result = await getCustomFieldId('simple-requirements-field');

      expect(result).toBeNull();
    });
  });

  describe('updateIssueFields', () => {
    const issueId = '10001';
    const fields = { 'customfield_10002': { type: 'doc', content: [] } };

    it('should successfully update issue fields', async () => {
      mockRequestJira.mockResolvedValue({
        ok: true,
        status: 200
      });

      const result = await updateIssueFields(issueId, fields);

      expect(result).toBe(true);
      expect(mockRequestJira).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'PUT',
          headers: expect.objectContaining({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({ fields })
        })
      );
    });

    it('should return false if update request fails', async () => {
      mockRequestJira.mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: jest.fn().mockResolvedValue('Update failed')
      });

      const result = await updateIssueFields(issueId, fields);

      expect(result).toBe(false);
    });

    it('should handle update request exceptions', async () => {
      mockRequestJira.mockRejectedValue(new Error('Network error'));

      const result = await updateIssueFields(issueId, fields);

      expect(result).toBe(false);
    });
  });

  describe('isFieldUpdated', () => {
    const mockField = { id: 'customfield_10001', name: 'Simple Requirements' };

    it('should return true if field was updated by fieldId', () => {
      const changelog = {
        items: [
          { fieldId: 'customfield_10001', field: 'Simple Requirements', from: 'old', to: 'new' }
        ]
      };

      const result = isFieldUpdated(changelog, mockField);

      expect(result).toBe(true);
    });

    it('should return true if field was updated by field name', () => {
      const changelog = {
        items: [
          { field: 'Simple Requirements', from: 'old', to: 'new' }
        ]
      };

      const result = isFieldUpdated(changelog, mockField);

      expect(result).toBe(true);
    });

    it('should return false if field was not updated', () => {
      const changelog = {
        items: [
          { fieldId: 'customfield_99999', field: 'Other Field', from: 'old', to: 'new' }
        ]
      };

      const result = isFieldUpdated(changelog, mockField);

      expect(result).toBe(false);
    });

    it('should return false if changelog is null', () => {
      const result = isFieldUpdated(null, mockField);

      expect(result).toBe(false);
    });

    it('should return false if changelog has no items', () => {
      const changelog = { items: null };

      const result = isFieldUpdated(changelog, mockField);

      expect(result).toBe(false);
    });

    it('should return false if field is null', () => {
      const changelog = {
        items: [
          { fieldId: 'customfield_10001', field: 'Simple Requirements', from: 'old', to: 'new' }
        ]
      };

      const result = isFieldUpdated(changelog, null);

      expect(result).toBe(false);
    });
  });

  describe('getFieldChangeValue', () => {
    const mockField = { id: 'customfield_10001', name: 'Simple Requirements' };

    it('should return new value when field was updated by fieldId', () => {
      const changelog = {
        items: [
          { fieldId: 'customfield_10001', field: 'Simple Requirements', from: 'old', to: 'new value' }
        ]
      };

      const result = getFieldChangeValue(changelog, mockField);

      expect(result).toBe('new value');
    });

    it('should return new value when field was updated by field name', () => {
      const changelog = {
        items: [
          { field: 'Simple Requirements', from: 'old', to: 'new value' }
        ]
      };

      const result = getFieldChangeValue(changelog, mockField);

      expect(result).toBe('new value');
    });

    it('should return null if field was not found in changelog', () => {
      const changelog = {
        items: [
          { fieldId: 'customfield_99999', field: 'Other Field', from: 'old', to: 'new' }
        ]
      };

      const result = getFieldChangeValue(changelog, mockField);

      expect(result).toBeNull();
    });

    it('should return null if changelog is null', () => {
      const result = getFieldChangeValue(null, mockField);

      expect(result).toBeNull();
    });

    it('should return null if field is null', () => {
      const changelog = {
        items: [
          { fieldId: 'customfield_10001', field: 'Simple Requirements', from: 'old', to: 'new value' }
        ]
      };

      const result = getFieldChangeValue(changelog, null);

      expect(result).toBeNull();
    });
  });

  describe('validateEnvironment', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      process.env = { ...originalEnv };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should return valid when all required environment variables are set', () => {
      process.env.SECRET_OPENROUTER_API_KEY = 'test-api-key';

      const result = validateEnvironment();

      expect(result).toEqual({
        isValid: true,
        missing: []
      });
    });

    it('should return invalid when required environment variables are missing', () => {
      delete process.env.SECRET_OPENROUTER_API_KEY;

      const result = validateEnvironment();

      expect(result).toEqual({
        isValid: false,
        missing: ['SECRET_OPENROUTER_API_KEY']
      });
    });

    it('should handle empty string environment variables as missing', () => {
      process.env.SECRET_OPENROUTER_API_KEY = '';

      const result = validateEnvironment();

      expect(result).toEqual({
        isValid: false,
        missing: ['SECRET_OPENROUTER_API_KEY']
      });
    });
  });
});