import { jest } from '@jest/globals';

// Create mock for @forge/api
const mockFetch = jest.fn();
const mockApi = {
  fetch: mockFetch
};

jest.unstable_mockModule('@forge/api', () => ({
  default: mockApi
}));

// Import the module under test after mocking
const { expandRequirements, validateOpenRouterConfig } = await import('../../../src/utils/openrouter-client.js');

// Test data
const mockExpandedRequirements = `# Expanded Requirements

## Overview
This is the expanded version of the requirements.

## Acceptance Criteria
- Criterion 1
- Criterion 2
- Criterion 3

## Technical Notes
Additional technical details here.`;

const createMockOpenRouterResponse = () => ({
  ok: true,
  status: 200,
  json: jest.fn().mockResolvedValue({
    choices: [
      {
        message: {
          content: mockExpandedRequirements
        }
      }
    ]
  })
});

const mockOpenRouterErrorResponse = {
  ok: false,
  status: 500,
  statusText: 'Internal Server Error',
  text: jest.fn().mockResolvedValue('API Error')
};

describe('openrouter-client', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env = { ...originalEnv };
    process.env.SECRET_OPENROUTER_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('expandRequirements', () => {
    const simpleRequirements = 'Create user login functionality';

    it('should successfully expand requirements', async () => {
      mockFetch.mockResolvedValue(createMockOpenRouterResponse());

      const result = await expandRequirements(simpleRequirements);

      expect(result).toBe(mockExpandedRequirements);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key',
            'HTTP-Referer': 'https://project0jiraplugin.atlassian.net',
            'X-Title': 'Jira Requirements Expander'
          }),
          body: expect.stringContaining(simpleRequirements)
        })
      );
    });

    it('should return null if API key is not set', async () => {
      delete process.env.SECRET_OPENROUTER_API_KEY;

      const result = await expandRequirements(simpleRequirements);

      expect(result).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should return null if simple requirements is empty', async () => {
      const result = await expandRequirements('');

      expect(result).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should return null if simple requirements is whitespace only', async () => {
      const result = await expandRequirements('   ');

      expect(result).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should return null if simple requirements is null', async () => {
      const result = await expandRequirements(null);

      expect(result).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should handle API error response', async () => {
      mockFetch.mockResolvedValue(mockOpenRouterErrorResponse);

      const result = await expandRequirements(simpleRequirements);

      expect(result).toBeNull();
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network failed'));

      const result = await expandRequirements(simpleRequirements);

      expect(result).toBeNull();
    });

    it('should handle timeout', async () => {
      const timeoutOptions = { timeoutMs: 100 };
      
      // Mock a delayed response that will timeout
      const delayedResponse = new Promise(resolve => {
        setTimeout(() => resolve(createMockOpenRouterResponse()), 200);
      });
      mockFetch.mockReturnValue(delayedResponse);

      const result = await expandRequirements(simpleRequirements, timeoutOptions);

      expect(result).toBeNull();
    });

    it('should handle unexpected response format', async () => {
      const unexpectedResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({
          error: 'Unexpected format'
        })
      };
      mockFetch.mockResolvedValue(unexpectedResponse);

      const result = await expandRequirements(simpleRequirements);

      expect(result).toBeNull();
    });

    it('should handle empty choices array', async () => {
      const emptyChoicesResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({
          choices: []
        })
      };
      mockFetch.mockResolvedValue(emptyChoicesResponse);

      const result = await expandRequirements(simpleRequirements);

      expect(result).toBeNull();
    });

    it('should use custom configuration options', async () => {
      const customOptions = {
        model: 'custom-model',
        temperature: 0.5,
        maxTokens: 1000,
        timeoutMs: 30000
      };
      mockFetch.mockResolvedValue(createMockOpenRouterResponse());

      const result = await expandRequirements(simpleRequirements, customOptions);

      expect(result).toBe(mockExpandedRequirements);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          body: expect.stringContaining('custom-model')
        })
      );
    });

    it('should make correct API request with proper headers and body', async () => {
      mockFetch.mockResolvedValue(createMockOpenRouterResponse());

      await expandRequirements(simpleRequirements);

      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key',
            'HTTP-Referer': 'https://project0jiraplugin.atlassian.net',
            'X-Title': 'Jira Requirements Expander'
          }),
          body: expect.stringContaining(simpleRequirements)
        })
      );
    });
  });

  describe('validateOpenRouterConfig', () => {
    it('should return valid when API key is set', () => {
      process.env.SECRET_OPENROUTER_API_KEY = 'test-api-key';

      const result = validateOpenRouterConfig();

      expect(result).toEqual({
        isValid: true,
        errors: []
      });
    });

    it('should return invalid when API key is not set', () => {
      delete process.env.SECRET_OPENROUTER_API_KEY;

      const result = validateOpenRouterConfig();

      expect(result).toEqual({
        isValid: false,
        errors: ['SECRET_OPENROUTER_API_KEY environment variable is not set']
      });
    });

    it('should return invalid when API key is empty string', () => {
      process.env.SECRET_OPENROUTER_API_KEY = '';

      const result = validateOpenRouterConfig();

      expect(result).toEqual({
        isValid: false,
        errors: ['SECRET_OPENROUTER_API_KEY environment variable is not set']
      });
    });
  });
});