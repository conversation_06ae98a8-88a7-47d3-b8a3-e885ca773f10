import { jest } from '@jest/globals';

// Create mock functions
const mockGetCustomFieldId = jest.fn();
const mockUpdateIssueFields = jest.fn();
const mockIsFieldUpdated = jest.fn();
const mockGetFieldChangeValue = jest.fn();
const mockValidateEnvironment = jest.fn();
const mockExpandRequirements = jest.fn();
const mockMarkdownToAdf = jest.fn();

// Mock modules using unstable_mockModule for ES modules
jest.unstable_mockModule('../../../src/utils/jira-helpers.js', () => ({
  getCustomFieldId: mockGetCustomFieldId,
  updateIssueFields: mockUpdateIssueFields,
  isFieldUpdated: mockIsFieldUpdated,
  getFieldChangeValue: mockGetFieldChangeValue,
  validateEnvironment: mockValidateEnvironment
}));

jest.unstable_mockModule('../../../src/utils/openrouter-client.js', () => ({
  expandRequirements: mockExpandRequirements
}));

jest.unstable_mockModule('marklassian', () => ({
  markdownToAdf: mockMarkdownToAdf
}));

// Import the module under test after mocking
const { simpleRequirementsUpdatedHandler } = await import('../../../src/resolvers/index.js');

// Test data
const mockSimpleField = { id: 'customfield_10001', name: 'Simple Requirements' };
const mockFullField = { id: 'customfield_10002', name: 'Full Requirements' };
const mockEvent = {
  issue: { id: '10001', key: 'TEST-1' },
  changelog: {
    items: [{ fieldId: 'customfield_10001', field: 'Simple Requirements', from: 'old', to: 'new requirements' }]
  }
};
const mockEventNoChangelog = { issue: { id: '10001', key: 'TEST-1' } };
const mockContext = { accountId: 'test-account-id' };

describe('simpleRequirementsUpdatedHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Environment validation', () => {
    it('should return early if environment validation fails', async () => {
      mockValidateEnvironment.mockReturnValue({
        isValid: false,
        missing: ['SECRET_OPENROUTER_API_KEY']
      });

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockValidateEnvironment).toHaveBeenCalled();
      expect(mockGetCustomFieldId).not.toHaveBeenCalled();
    });

    it('should continue processing if environment validation passes', async () => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(false);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockValidateEnvironment).toHaveBeenCalled();
      expect(mockGetCustomFieldId).toHaveBeenCalledTimes(2);
    });
  });

  describe('Custom field resolution', () => {
    beforeEach(() => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
    });

    it('should return early if simple requirements field cannot be resolved', async () => {
      mockGetCustomFieldId.mockResolvedValueOnce(null);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetCustomFieldId).toHaveBeenCalledWith('simple-requirements-field');
      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
    });

    it('should return early if full requirements field cannot be resolved', async () => {
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(null);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetCustomFieldId).toHaveBeenCalledWith('simple-requirements-field');
      expect(mockGetCustomFieldId).toHaveBeenCalledWith('full-requirements-field');
      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
    });

    it('should proceed if both fields are resolved successfully', async () => {
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(false);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetCustomFieldId).toHaveBeenCalledWith('simple-requirements-field');
      expect(mockGetCustomFieldId).toHaveBeenCalledWith('full-requirements-field');
      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
    });
  });

  describe('Changelog validation', () => {
    beforeEach(() => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
    });

    it('should return early if no changelog is present', async () => {
      await simpleRequirementsUpdatedHandler(mockEventNoChangelog, mockContext);

      expect(mockIsFieldUpdated).not.toHaveBeenCalled();
    });

    it('should return early if simple requirements field was not updated', async () => {
      mockIsFieldUpdated.mockReturnValue(false);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockGetFieldChangeValue).not.toHaveBeenCalled();
    });

    it('should proceed if simple requirements field was updated', async () => {
      mockIsFieldUpdated.mockReturnValue(true);
      mockGetFieldChangeValue.mockReturnValue('');

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockGetFieldChangeValue).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
    });
  });

  describe('Requirements processing', () => {
    beforeEach(() => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(true);
    });

    it('should return early if simple requirements is empty', async () => {
      mockGetFieldChangeValue.mockReturnValue('');

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetFieldChangeValue).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockExpandRequirements).not.toHaveBeenCalled();
    });

    it('should return early if simple requirements is whitespace only', async () => {
      mockGetFieldChangeValue.mockReturnValue('   ');

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockExpandRequirements).not.toHaveBeenCalled();
    });

    it('should proceed with AI expansion if requirements are provided', async () => {
      const simpleRequirements = 'Create user login functionality';
      mockGetFieldChangeValue.mockReturnValue(simpleRequirements);
      mockExpandRequirements.mockResolvedValue(null);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockGetFieldChangeValue).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockExpandRequirements).toHaveBeenCalledWith(simpleRequirements);
    });

    it('should return early if AI expansion fails', async () => {
      const simpleRequirements = 'Create user login functionality';
      mockGetFieldChangeValue.mockReturnValue(simpleRequirements);
      mockExpandRequirements.mockResolvedValue(null);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockExpandRequirements).toHaveBeenCalledWith(simpleRequirements);
      expect(mockMarkdownToAdf).not.toHaveBeenCalled();
    });
  });

  describe('Full requirements update', () => {
    const mockExpandedRequirements = '# Expanded Requirements\n\nDetailed requirements here.';
    const mockAdfContent = { type: 'doc', content: [] };

    beforeEach(() => {
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(true);
      mockGetFieldChangeValue.mockReturnValue('Create user login functionality');
      mockExpandRequirements.mockResolvedValue(mockExpandedRequirements);
    });

    it('should convert markdown to ADF and update the issue successfully', async () => {
      mockMarkdownToAdf.mockReturnValue(mockAdfContent);
      mockUpdateIssueFields.mockResolvedValue(true);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockExpandRequirements).toHaveBeenCalledWith('Create user login functionality');
      expect(mockMarkdownToAdf).toHaveBeenCalledWith(mockExpandedRequirements);
      expect(mockUpdateIssueFields).toHaveBeenCalledWith(mockEvent.issue.id, {
        [mockFullField.id]: mockAdfContent
      });
    });

    it('should handle issue update failure', async () => {
      mockMarkdownToAdf.mockReturnValue(mockAdfContent);
      mockUpdateIssueFields.mockResolvedValue(false);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      expect(mockUpdateIssueFields).toHaveBeenCalledWith(mockEvent.issue.id, {
        [mockFullField.id]: mockAdfContent
      });
      // No additional verification needed - we just need to ensure it doesn't throw
    });
  });

  describe('Complete workflow', () => {
    it('should execute complete successful workflow', async () => {
      const simpleRequirements = 'Create user login functionality';
      const mockExpandedRequirements = '# Expanded Requirements\n\nDetailed requirements here.';
      const mockAdfContent = { type: 'doc', content: [] };
      
      // Setup all mocks for successful execution
      mockValidateEnvironment.mockReturnValue({ isValid: true, missing: [] });
      mockGetCustomFieldId.mockResolvedValueOnce(mockSimpleField);
      mockGetCustomFieldId.mockResolvedValueOnce(mockFullField);
      mockIsFieldUpdated.mockReturnValue(true);
      mockGetFieldChangeValue.mockReturnValue(simpleRequirements);
      mockExpandRequirements.mockResolvedValue(mockExpandedRequirements);
      mockMarkdownToAdf.mockReturnValue(mockAdfContent);
      mockUpdateIssueFields.mockResolvedValue(true);

      await simpleRequirementsUpdatedHandler(mockEvent, mockContext);

      // Verify all steps were executed in correct order
      expect(mockValidateEnvironment).toHaveBeenCalled();
      expect(mockGetCustomFieldId).toHaveBeenCalledTimes(2);
      expect(mockIsFieldUpdated).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockGetFieldChangeValue).toHaveBeenCalledWith(mockEvent.changelog, mockSimpleField);
      expect(mockExpandRequirements).toHaveBeenCalledWith(simpleRequirements);
      expect(mockMarkdownToAdf).toHaveBeenCalledWith(mockExpandedRequirements);
      expect(mockUpdateIssueFields).toHaveBeenCalledWith(mockEvent.issue.id, {
        [mockFullField.id]: mockAdfContent
      });
    });
  });
});