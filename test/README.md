# Integration Tests

This directory contains integration tests for the RequirementsUpdateHandler Forge app.

## Prerequisites

Before running the integration tests, ensure the following:

1. **Forge App Deployed**: The Forge app must be deployed and running in your Jira instance
2. **Test Issue**: Issue with ID `10139` (or your configured test issue) must exist in your Jira instance
3. **Custom Fields**: Both "Simple Requirements" and "Full Requirements" custom fields must be available
4. **API Configuration**: OpenRouter API key must be configured in the Forge environment variables
5. **Permissions**: The app must have the necessary permissions to read and write Jira issues
6. **Local Configuration**: Jira API credentials must be configured for local testing (see Configuration section)

## Configuration

The integration tests require Jira API credentials to run locally. Set up your configuration:

### Option 1: Environment File (Recommended)

1. Copy the example configuration file:
   ```bash
   cp test/.env.example test/.env
   ```

2. Edit `test/.env` with your actual values:
   ```bash
   # Your Jira instance URL (without trailing slash)
   JIRA_BASE_URL=https://your-domain.atlassian.net

   # Your Jira email address
   JIRA_EMAIL=<EMAIL>

   # Your Jira API token
   JIRA_ACCESS_TOKEN=your_api_token_here

   # Optional: Test issue ID (defaults to 10139)
   TEST_ISSUE_ID=10139
   ```

### Option 2: Environment Variables

Set the environment variables directly:
```bash
export JIRA_BASE_URL="https://your-domain.atlassian.net"
export JIRA_EMAIL="<EMAIL>"
export JIRA_ACCESS_TOKEN="your_api_token_here"
export TEST_ISSUE_ID="10139"
```

### Creating a Jira API Token

1. Go to [Atlassian Account Security](https://id.atlassian.com/manage-profile/security/api-tokens)
2. Click "Create API token"
3. Give it a label (e.g., "Integration Tests")
4. Copy the generated token and use it as `JIRA_ACCESS_TOKEN`

## Running the Tests

### Option 1: Using npm scripts

```bash
# Run all tests
npm test

# Run only integration tests
npm run test:integration
```

### Option 2: Using the test runner directly

```bash
node test/run-integration-test.js
```

### Option 3: Using Node.js test runner directly

```bash
node --test test/integration/**/*.test.js
```

## Test Description

### RequirementsUpdateHandler Integration Test

This test verifies the complete workflow of the RequirementsUpdateHandler:

1. **Setup**: 
   - Retrieves the custom field IDs for "Simple Requirements" and "Full Requirements"
   - Stores the original values for cleanup

2. **Test Execution**:
   - Updates the SimpleRequirements field with test content: "Test requirement: Create a user login system with authentication"
   - Clears the FullRequirements field (sets to null)
   - Waits 25 seconds for the handler to process the update
   - Verifies that the FullRequirements field was updated with new content

3. **Cleanup**:
   - Restores the original field values

## Expected Behavior

When the SimpleRequirements field is updated, the RequirementsUpdateHandler should:

1. Detect the field change via the Jira trigger
2. Call the OpenRouter API to expand the simple requirements
3. Convert the response to ADF format
4. Update the FullRequirements field with the expanded content

## Troubleshooting

### Common Issues

1. **Configuration Not Set**: Ensure you've created `test/.env` with your Jira credentials
2. **Invalid API Token**: Verify your Jira API token is correct and hasn't expired
3. **Wrong Jira URL**: Ensure your `JIRA_BASE_URL` is correct (e.g., `https://your-domain.atlassian.net`)
4. **Field Not Found**: Ensure both custom fields are properly defined in the manifest.yml and deployed
5. **Permission Denied**: Verify the app has `read:jira-work` and `write:jira-work` permissions
6. **API Key Missing**: Check that `SECRET_OPENROUTER_API_KEY` is set in Forge environment variables
7. **Issue Not Found**: Ensure the test issue exists in your Jira instance
8. **Timeout**: The test waits 25 seconds - if your handler takes longer, you may need to adjust the wait time

### Debug Information

The test provides detailed console output including:
- Field IDs being used
- Original field values
- Update operations
- Final field values

### Manual Verification

You can also manually verify the handler by:
1. Opening issue 10139 in Jira
2. Updating the "Simple Requirements" field
3. Waiting for the "Full Requirements" field to be automatically updated

## Test Configuration

- **Test Issue ID**: 10139 (hardcoded in the test)
- **Wait Time**: 25 seconds (configurable in the test)
- **Test Input**: "Test requirement: Create a user login system with authentication"

To modify these values, edit the test file: `test/integration/requirements-update-handler.test.js`
