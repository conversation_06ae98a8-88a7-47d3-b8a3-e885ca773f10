#!/usr/bin/env node

/**
 * Manual Test Script for RequirementsUpdateHandler
 * 
 * This script allows you to manually test the handler by updating
 * the SimpleRequirements field and monitoring the FullRequirements field.
 * 
 * Usage:
 *   node test/manual-test.js
 */

import { config, validateConfig } from './config.js';
import { getCustomFieldId, getIssue, updateIssue } from './utils/jira-api-client.js';
import { wait, createTestInput } from './utils/test-helpers.js';

async function main() {
  console.log('🧪 Manual Test for RequirementsUpdateHandler');
  console.log('');
  
  try {
    // Validate configuration
    validateConfig();
    console.log('');
    
    // Get field IDs
    console.log('🔍 Getting custom field IDs...');
    const simpleReqFieldId = await getCustomFieldId('Simple Requirements');
    const fullReqFieldId = await getCustomFieldId('Full Requirements');
    
    console.log(`✅ Simple Requirements Field ID: ${simpleReqFieldId}`);
    console.log(`✅ Full Requirements Field ID: ${fullReqFieldId}`);
    console.log('');
    
    // Get current state
    console.log('📋 Current issue state:');
    let issue = await getIssue(config.TEST_ISSUE_ID);
    console.log(`   Issue: ${issue.key} - ${issue.fields.summary}`);
    console.log(`   Simple Requirements: ${issue.fields[simpleReqFieldId] || 'null'}`);
    console.log(`   Full Requirements: ${issue.fields[fullReqFieldId] ? 'Has content' : 'null'}`);
    console.log('');
    
    // Update the field
    const testInput = createTestInput('Manual test: Create a responsive dashboard');
    console.log('🔄 Updating SimpleRequirements field...');
    console.log(`   Input: "${testInput}"`);
    
    await updateIssue(config.TEST_ISSUE_ID, {
      [simpleReqFieldId]: testInput,
      [fullReqFieldId]: null // Clear the full requirements
    });
    
    console.log('✅ Field updated successfully');
    console.log('');
    
    // Monitor for changes
    console.log('👀 Monitoring FullRequirements field for changes...');
    console.log('   Press Ctrl+C to stop monitoring');
    console.log('');
    
    let previousValue = null;
    let checkCount = 0;
    
    while (true) {
      await wait(5);
      checkCount++;
      
      issue = await getIssue(config.TEST_ISSUE_ID);
      const currentValue = issue.fields[fullReqFieldId];
      
      if (currentValue !== previousValue) {
        console.log(`📝 Change detected after ${checkCount * 5} seconds:`);
        if (currentValue === null) {
          console.log('   FullRequirements: null');
        } else if (typeof currentValue === 'string') {
          console.log(`   FullRequirements: "${currentValue.substring(0, 100)}..."`);
        } else {
          console.log('   FullRequirements: [ADF Content]');
        }
        
        if (currentValue !== null && previousValue === null) {
          console.log('');
          console.log('🎉 SUCCESS! The RequirementsUpdateHandler is working!');
          console.log('   The FullRequirements field was automatically updated.');
          break;
        }
        
        previousValue = currentValue;
      } else {
        process.stdout.write(`   Check ${checkCount} (${checkCount * 5}s): No change\r`);
      }
      
      // Stop after 2 minutes
      if (checkCount >= 24) {
        console.log('');
        console.log('⏰ Stopped monitoring after 2 minutes');
        console.log('');
        console.log('💡 If no changes were detected, this could indicate:');
        console.log('   - The Forge app is not deployed or running');
        console.log('   - The trigger is not configured correctly');
        console.log('   - The OpenRouter API key is missing');
        console.log('   - The handler is encountering errors');
        console.log('');
        console.log('🔧 Try checking the Forge logs: forge logs');
        break;
      }
    }
    
  } catch (error) {
    console.error('❌ Manual test failed:', error.message);
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('');
  console.log('👋 Manual test stopped by user');
  process.exit(0);
});

main().catch(console.error);
