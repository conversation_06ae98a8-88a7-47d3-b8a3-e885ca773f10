/**
 * Test Helper Functions
 * 
 * This module contains common utility functions used across different test files.
 */

/**
 * Wait for a specified number of seconds
 * @param {number} seconds - Number of seconds to wait
 * @returns {Promise<void>}
 */
export function wait(seconds) {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

/**
 * Poll for a condition to become true with timeout
 * @param {Function} conditionFn - Function that returns true when condition is met
 * @param {Object} options - Polling options
 * @param {number} options.intervalMs - Polling interval in milliseconds (default: 5000)
 * @param {number} options.timeoutMs - Timeout in milliseconds (default: 60000)
 * @param {string} options.description - Description for logging (default: 'condition')
 * @returns {Promise<boolean>} True if condition was met, false if timeout
 */
export async function pollForCondition(conditionFn, options = {}) {
  const {
    intervalMs = 5000,
    timeoutMs = 60000,
    description = 'condition'
  } = options;

  const startTime = Date.now();
  const maxAttempts = Math.ceil(timeoutMs / intervalMs);
  let attempts = 0;

  console.log(`🔄 Polling for ${description} (max ${timeoutMs/1000}s)...`);

  while (attempts < maxAttempts) {
    attempts++;
    const elapsed = Date.now() - startTime;

    try {
      const result = await conditionFn();
      if (result) {
        console.log(`✅ ${description} met after ${Math.round(elapsed/1000)}s (attempt ${attempts})`);
        return true;
      }
    } catch (error) {
      console.log(`⚠️  Error checking ${description} on attempt ${attempts}: ${error.message}`);
    }

    if (elapsed >= timeoutMs) {
      break;
    }

    console.log(`   Attempt ${attempts}/${maxAttempts} (${Math.round(elapsed/1000)}s): ${description} not met yet...`);
    await wait(intervalMs / 1000);
  }

  console.log(`❌ ${description} not met within ${timeoutMs/1000}s timeout`);
  return false;
}

/**
 * Validate ADF (Atlassian Document Format) content structure
 * @param {any} content - The content to validate
 * @returns {Object} Validation result with isValid boolean and errors array
 */
export function validateAdfContent(content) {
  const errors = [];

  if (!content) {
    errors.push('Content is null or undefined');
    return { isValid: false, errors };
  }

  if (typeof content !== 'object') {
    errors.push('Content is not an object');
    return { isValid: false, errors };
  }

  if (content.type !== 'doc') {
    errors.push('Content type is not "doc"');
  }

  if (!content.content || !Array.isArray(content.content)) {
    errors.push('Content does not have a valid content array');
  } else if (content.content.length === 0) {
    errors.push('Content array is empty');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Generate a unique test identifier
 * @param {string} prefix - Prefix for the identifier (default: 'test')
 * @returns {string} Unique identifier
 */
export function generateTestId(prefix = 'test') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}-${timestamp}-${random}`;
}

/**
 * Create a test input with timestamp for uniqueness
 * @param {string} baseInput - Base input text
 * @returns {string} Test input with timestamp
 */
export function createTestInput(baseInput) {
  const timestamp = new Date().toISOString();
  return `${baseInput} - Test run: ${timestamp}`;
}

/**
 * Log test step with formatting
 * @param {number} stepNumber - Step number
 * @param {string} description - Step description
 * @param {any} details - Optional details to log
 */
export function logTestStep(stepNumber, description, details = null) {
  console.log(`\n📋 Step ${stepNumber}: ${description}`);
  if (details) {
    console.log(`   Details: ${typeof details === 'string' ? details : JSON.stringify(details)}`);
  }
}

/**
 * Log test result with formatting
 * @param {boolean} success - Whether the test was successful
 * @param {string} message - Result message
 * @param {any} data - Optional data to log
 */
export function logTestResult(success, message, data = null) {
  const icon = success ? '✅' : '❌';
  console.log(`\n${icon} ${message}`);
  if (data) {
    console.log(`   Data: ${typeof data === 'string' ? data : JSON.stringify(data, null, 2)}`);
  }
}

/**
 * Create debugging information for failed tests
 * @param {string} testName - Name of the test
 * @param {Error} error - The error that occurred
 * @param {Object} context - Additional context information
 * @returns {Object} Formatted debugging information
 */
export function createDebugInfo(testName, error, context = {}) {
  return {
    test: testName,
    error: {
      message: error.message,
      stack: error.stack
    },
    timestamp: new Date().toISOString(),
    context
  };
}

/**
 * Retry a function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {Object} options - Retry options
 * @param {number} options.maxAttempts - Maximum number of attempts (default: 3)
 * @param {number} options.baseDelayMs - Base delay in milliseconds (default: 1000)
 * @param {number} options.maxDelayMs - Maximum delay in milliseconds (default: 10000)
 * @returns {Promise<any>} Result of the function
 */
export async function retryWithBackoff(fn, options = {}) {
  const {
    maxAttempts = 3,
    baseDelayMs = 1000,
    maxDelayMs = 10000
  } = options;

  let lastError;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxAttempts) {
        throw error;
      }

      const delay = Math.min(baseDelayMs * Math.pow(2, attempt - 1), maxDelayMs);
      console.log(`⚠️  Attempt ${attempt} failed, retrying in ${delay}ms: ${error.message}`);
      await wait(delay / 1000);
    }
  }

  throw lastError;
}
