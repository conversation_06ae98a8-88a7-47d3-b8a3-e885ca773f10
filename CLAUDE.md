# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Jira Forge app that provides AI-powered requirements expansion. When users update the "Simple Requirements" custom field in Jira issues, the app automatically expands them into detailed requirements using OpenRouter AI and populates the "Full Requirements" field.

## Common Commands

### Testing
- `npm test` - Run all tests
- `npm run test:unit` - Run unit tests with custom test runner
- `npm run test:integration` - Run end-to-end integration tests
- `npm run test:validate-config` - Validate test configuration
- `npm run test:manual` - Run manual testing utilities

### Forge Development
- `forge lint` - Run ESLint on source code
- `forge deploy` - Deploy app changes to Forge platform
- `forge install` - Install app on Jira site
- `forge tunnel` - Run local development proxy for testing

## Architecture

### Core Flow
1. **Trigger**: Jira `avi:jira:updated:issue` event fires when issues are updated
2. **Handler**: `simpleRequirementsUpdatedHandler` in `src/resolvers/index.js` processes events
3. **Validation**: Checks if Simple Requirements field was updated and environment is configured
4. **AI Processing**: Calls OpenRouter API via `src/utils/openrouter-client.js` to expand requirements
5. **Format Conversion**: Uses `marklassian` to convert Markdown response to ADF
6. **Update**: Populates Full Requirements field via Jira REST API

### Key Components

#### Resolvers (`src/resolvers/index.js`)
- Main event handler with comprehensive validation and error handling
- Integrates all utility modules for complete workflow
- Handles dynamic custom field ID resolution

#### Utilities
- **`src/utils/jira-helpers.js`**: Jira API interactions, field resolution, change detection
- **`src/utils/openrouter-client.js`**: AI service integration with DeepSeek model

#### Custom Fields (defined in `manifest.yml`)
- `simple-requirements-field`: Input for basic requirements
- `full-requirements-field`: Auto-populated with AI-expanded requirements

### Testing Architecture

The project uses a custom testing framework with comprehensive mocking:

- **Unit Tests** (`test/unit/`): Test individual functions with mocked dependencies
- **Integration Tests** (`test/integration/`): Test complete workflows end-to-end
- **Mock System**: Custom mocks in `test/unit/mocks/` for Forge API and external services
- **Test Runner**: Custom unit test runner with colored output and detailed reporting

### Dependencies
- **`@forge/api`**: Core Forge platform integration
- **`@forge/resolver`**: Event handling framework
- **`marklassian`**: Markdown to ADF conversion for Jira rich text
- **OpenRouter**: External AI service (DeepSeek Chat v3 model)

## Environment Configuration

Required environment variables:
- `OPENROUTER_API_KEY`: API key for OpenRouter service
- Test configurations use `.env` files in test directories

## Development Notes

### Custom Field Resolution
The app dynamically resolves custom field IDs by name rather than hardcoding them, making it portable across different Jira instances.

### AI Integration
- Uses DeepSeek Chat v3 model via OpenRouter (free tier)
- Configured with 0.7 temperature, 500 max tokens, 20-second timeout
- Structured prompts for consistent Markdown-formatted responses

### Error Handling
Comprehensive error handling throughout with specific validation for:
- Environment configuration
- Field existence and updates
- API timeouts and failures
- Format conversions

### Testing Patterns
- Proper setup/teardown in test environments
- Mock data generators in `test-config.js`
- Environment isolation between test types
- Comprehensive edge case coverage